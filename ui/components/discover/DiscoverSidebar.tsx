import React from 'react';
import { cn } from '@/lib/utils';
import { Topic } from '@/types/discover';

interface DiscoverSidebarProps {
  topics: Topic[];
  selectedTopicId: string;
  onSelectTopic: (topicId: string, index: number) => void;
  className?: string;
}

const DiscoverSidebar: React.FC<DiscoverSidebarProps> = ({
  topics,
  selectedTopicId,
  onSelectTopic,
  className = '',
}) => {
  return (
    <aside
      className={cn(
        'hidden lg:block w-56 min-w-[13rem] h-full bg-gradient-to-b from-white/95 via-blue-50/80 to-white/95 dark:from-[#181c20]/95 dark:via-blue-900/20 dark:to-[#181c20]/95 py-6 px-3 space-y-2 rounded-2xl shadow-md border border-light-200 dark:border-dark-200',
        className
      )}
    >
      <h2 className="text-base font-semibold mb-3 px-2 text-gray-700 dark:text-gray-200">主题导航</h2>
      <nav className="flex-1 flex flex-col gap-2 overflow-y-auto">
        {topics.map((topic, idx) => (
          <button
            key={topic.id}
            className={cn(
              'text-left px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 shadow-sm',
              selectedTopicId === topic.id
                ? 'bg-blue-500 text-white shadow-md'
                : 'bg-gray-100 dark:bg-dark-100 text-gray-500 dark:text-gray-300 hover:bg-blue-100 dark:hover:bg-blue-900/30',
            )}
            onClick={() => onSelectTopic(topic.id, idx)}
          >
            {topic.name}
          </button>
        ))}
      </nav>
    </aside>
  );
};

export default DiscoverSidebar;