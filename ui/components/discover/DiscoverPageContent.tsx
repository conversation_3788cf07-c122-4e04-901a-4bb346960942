'use client';

import { useEffect, useState, useCallback } from 'react';
import { toast } from 'sonner';
import { cn, isAndroid } from '@/lib/utils';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import Layout from '@/components/Layout';
import DiscoverTopBar from '@/components/discover/DiscoverTopBar';
import DiscoverSidebar from '@/components/discover/DiscoverSidebar';
import { ArticleItem } from '@/components/discover/ArticleItem';
import { ChatDrawer } from '@/components/discover/ChatDrawer';
import { Discover, Topic } from '@/types/discover';

interface TopicData {
  blogs: Discover[];
  pagination: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

const DiscoverPageContent = () => {
  const [topics, setTopics] = useState<Topic[]>([]);
  const [discoverMap, setDiscoverMap] = useState<Record<string, TopicData>>({});
  const [currentPage, setCurrentPage] = useState<Record<string, number>>({});
  const [loading, setLoading] = useState(true);
  const [expandedItems, setExpandedItems] = useState<number[]>([]);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectedTab, setSelectedTab] = useState(0);
  const [summarizedOnly, setSummarizedOnly] = useState(false);
  const [importantOnly, setImportantOnly] = useState(false);
  const [selectedChat, setSelectedChat] = useState<{
    chatId?: string;
    discoverId: string;
    url?: string;
    title?: string;
  } | null>(null);
  const toggleExpand = (index: number) => {
    setExpandedItems(prev =>
      prev.includes(index)
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };
  const [topicLoading, setTopicLoading] = useState<Record<string, boolean>>({});
  const [isAndroidDevice, setIsAndroidDevice] = useState(false);

  const fetchTopicData = async (
    topic: string,
    page: number = 1,
    summarized: boolean = summarizedOnly,
    important: boolean = importantOnly
  ) => {
    setTopicLoading(prev => ({ ...prev, [topic]: true }));
    try {
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/discover?topic=${topic}&page=${page}&pageSize=10&summarized=${summarized}&important=${important}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      const data = await res.json();

      if (!res.ok) {
        throw new Error(data.message);
      }

      setDiscoverMap(prev => ({
        ...prev,
        [topic]: data,
      }));

      setCurrentPage(prev => ({
        ...prev,
        [topic]: page,
      }));

      setTimeout(() => {
        const tabPanel = document.querySelector(`[data-topic="${topic}"]`);
        if (tabPanel) {
          tabPanel.scrollTop = 0;
        }
      }, 0);
    } catch (err: any) {
      console.error('Error fetching data:', err.message);
      toast.error('获取数据失败');
    } finally {
      setTopicLoading(prev => ({ ...prev, [topic]: false }));
    }
  };

  const fetchTopics = async () => {
    try {
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/discover/topics`);
      const data = await res.json();
      if (!res.ok) {
        throw new Error(data.message);
      }
      setTopics(data.topics);
      return data.topics;
    } catch (err: any) {
      console.error('Error fetching topics:', err.message);
      toast.error('获取主题列表失败');
      return [];
    }
  };

  useEffect(() => {
    const init = async () => {
      try {
        const topicList = await fetchTopics();
        if (topicList.length > 0) {
          const savedTab = localStorage.getItem('selectedDiscoverTab');
          const savedTopicId = savedTab || topicList[0].id;
          const tabIndex = topicList.findIndex((t: { id: any }) => t.id === savedTopicId);

          setSelectedTab(tabIndex >= 0 ? tabIndex : 0);
          await fetchTopicData(savedTopicId);
        }
      } finally {
        setLoading(false);
      }
    };

    init();
  }, []);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsAndroidDevice(isAndroid());
    }
  }, []);

  const [refreshing, setRefreshing] = useState(false);
  let lastY = 0;

  const handlePullToRefresh = useCallback(
    async (topic: string) => {
      setRefreshing(true);
      try {
        await fetchTopicData(topic, 1);
        toast.success('数据已更新', {
          duration: 1500,
        });
      } finally {
        setRefreshing(false);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  const [pullDistance, setPullDistance] = useState(0);
  const [isPulling, setIsPulling] = useState(false);
  const threshold = 70;

  const handleTouchStart = (e: React.TouchEvent) => {
    lastY = e.touches[0].clientY;
    setIsPulling(false);
    setPullDistance(0);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    const panel = e.currentTarget;
    const y = e.touches[0].clientY;
    const isAtTop = panel.scrollTop <= 0;

    if (isAtTop && !refreshing) {
      const distance = Math.max(0, Math.min(y - lastY, threshold));
      if (y > lastY) {
        setPullDistance(distance);
        setIsPulling(true);
        e.preventDefault();
      }
    }
  };

  const handleTouchEnd = async (e: React.TouchEvent, topic: string) => {
    if (isPulling && pullDistance >= threshold) {
      setRefreshing(true);
      setPullDistance(threshold / 2);
      await handlePullToRefresh(topic);
    }
    setPullDistance(0);
    setIsPulling(false);
  };

  // 主题切换统一处理
  const handleSelectTopic = (topicId: string, idx: number) => {
    setSelectedTab(idx);
    localStorage.setItem('selectedDiscoverTab', topicId);
    if (!discoverMap[topicId]) {
      fetchTopicData(topicId);
    }
  };

  // Tab 切换（移动端）
  const handleTabSelect = (idx: number) => {
    const topic = topics[idx];
    if (!topic) return;
    handleSelectTopic(topic.id, idx);
  };

  const selectedTopic = topics[selectedTab];

  return loading ? (
    <LoadingSpinner />
  ) : (
    <Layout>
      <div className="flex flex-col min-h-screen bg-light-50 dark:bg-dark-50">
        <DiscoverTopBar
          topics={topics}
          selectedTopicId={selectedTopic?.id}
          onSelectTopic={handleSelectTopic}
          summarizedOnly={summarizedOnly}
          importantOnly={importantOnly}
          onSummarizedChange={checked => {
            setSummarizedOnly(checked);
            topics.forEach(topic => {
              fetchTopicData(topic.id, 1, checked, importantOnly);
            });
          }}
          onImportantChange={checked => {
            setImportantOnly(checked);
            topics.forEach(topic => {
              if (discoverMap[topic.id]) {
                fetchTopicData(topic.id, 1, summarizedOnly, checked);
              }
            });
          }}
        />
        <main className="flex-1 flex flex-col pt-[104px] lg:pt-[72px]">
          <div className="flex flex-1 min-h-0">
            <DiscoverSidebar
              topics={topics}
              selectedTopicId={selectedTopic?.id}
              onSelectTopic={handleSelectTopic}
              className="h-full"
            />
            <div className="flex-1 lg:pl-5 pr-1 lg:pr-5">
              <div className="flex-1 min-h-0 relative">
                {topics.length > 0 && selectedTopic && (
                  <div
                    className="flex-1 min-h-0 relative"
                    data-topic={selectedTopic.id}
                    onTouchStart={handleTouchStart}
                    onTouchMove={handleTouchMove}
                    onTouchEnd={e => handleTouchEnd(e, selectedTopic.id)}
                  >
                    <div
                      className="transition-transform duration-500 ease-in-out"
                      style={{
                        transform: `translateY(${pullDistance}px)`,
                      }}
                    >
                      <div
                        className="absolute top-0 left-0 right-0 flex justify-center items-center h-10 -translate-y-full transition-opacity duration-300"
                        style={{ opacity: isPulling || refreshing ? 1 : 0 }}
                      >
                        {refreshing ? (
                          <LoadingSpinner />
                        ) : (
                          <span className="text-xs text-black/70 dark:text-white/70">
                            {pullDistance >= threshold ? '松开刷新' : '下拉刷新'}
                          </span>
                        )}
                      </div>
                      <div className="flex flex-col space-y-1.5 p-1 pb-5">
                        {discoverMap[selectedTopic.id]?.blogs?.map((item, i) => (
                          <ArticleItem
                            key={item.id}
                            item={item}
                            index={i}
                            expandedItems={expandedItems}
                            toggleExpand={toggleExpand}
                            onSelect={item => {
                              setSelectedChat({
                                chatId: item.chatId,
                                discoverId: item.id,
                                url: !item.chatId ? item.url : undefined,
                                title: `追踪： ${item.titleZh || item.title}`,
                              });
                              setIsDrawerOpen(true);
                            }}
                          />
                        ))}
                      </div>
                      {/* 分页控制 */}
                      {discoverMap[selectedTopic.id]?.pagination && (
                        <div className="sticky bottom-0 z-10 flex justify-center items-center space-x-2 py-2 bg-light-50/80 dark:bg-dark-50/80 backdrop-blur-sm border-t border-black/5 dark:border-white/5">
                          <button
                            onClick={() =>
                              fetchTopicData(selectedTopic.id, currentPage[selectedTopic.id] - 1)
                            }
                            disabled={currentPage[selectedTopic.id] <= 1}
                            className="px-3 py-1.5 rounded text-sm disabled:opacity-50 hover:bg-light-100 dark:hover:bg-dark-100 transition-colors"
                          >
                            上一页
                          </button>
                          <span className="text-sm text-black/70 dark:text-white/70">
                            {currentPage[selectedTopic.id]} /{' '}
                            {discoverMap[selectedTopic.id].pagination.totalPages}
                          </span>
                          <button
                            onClick={() =>
                              fetchTopicData(selectedTopic.id, currentPage[selectedTopic.id] + 1)
                            }
                            disabled={
                              currentPage[selectedTopic.id] >=
                              discoverMap[selectedTopic.id].pagination.totalPages
                            }
                            className="px-3 py-1.5 rounded text-sm disabled:opacity-50 hover:bg-light-100 dark:hover:bg-dark-100 transition-colors"
                          >
                            下一页
                          </button>
                        </div>
                      )}
                    </div>
                    {topicLoading[selectedTopic.id] && (
                      <div className="absolute inset-0 bg-black/5 dark:bg-white/5 flex items-center justify-center z-10 backdrop-blur-[1px]">
                        <LoadingSpinner />
                      </div>
                    )}
                  </div>
                )}
              </div>
              {/* 右侧抽屉 */}
              {isDrawerOpen && (
                <ChatDrawer
                  isOpen={isDrawerOpen}
                  onClose={() => {
                    setIsDrawerOpen(false);
                    setSelectedChat(null);
                  }}
                  selectedChat={selectedChat}
                  onChatIdCreated={newChatId => {
                    setSelectedChat(prev =>
                      prev
                        ? {
                            ...prev,
                            chatId: newChatId,
                          }
                        : null
                    );
  
                    if (selectedChat?.discoverId) {
                      setDiscoverMap(prev => {
                        const newMap = { ...prev };
                        Object.keys(newMap).forEach(topic => {
                          if (newMap[topic]?.blogs) {
                            newMap[topic] = {
                              ...newMap[topic],
                              blogs: newMap[topic].blogs.map(item =>
                                item.id === selectedChat.discoverId
                                  ? { ...item, chatId: newChatId }
                                  : item
                              ),
                            };
                          }
                        });
                        return newMap;
                      });
                    }
                  }}
                />
              )}
            </div>
          </div>
        </main>
      </div>
    </Layout>
  );
};

export default DiscoverPageContent;
