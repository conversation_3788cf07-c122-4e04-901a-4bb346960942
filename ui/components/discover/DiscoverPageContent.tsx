'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import Layout from '@/components/Layout';
import DiscoverTopBar from '@/components/discover/DiscoverTopBar';
import DiscoverSidebar from '@/components/discover/DiscoverSidebar';
import { ArticleItem } from '@/components/discover/ArticleItem';
import { ChatDrawer } from '@/components/discover/ChatDrawer';
import { Discover, Topic } from '@/types/discover';

interface TopicData {
  blogs: Discover[];
  pagination: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

const DiscoverPageContent = () => {
  const [topics, setTopics] = useState<Topic[]>([]);
  const [discoverMap, setDiscoverMap] = useState<Record<string, TopicData>>({});
  const [currentPage, setCurrentPage] = useState<Record<string, number>>({});
  const [loading, setLoading] = useState(true);
  const [expandedItems, setExpandedItems] = useState<number[]>([]);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectedTab, setSelectedTab] = useState(0);
  const [summarizedOnly, setSummarizedOnly] = useState(false);
  const [importantOnly, setImportantOnly] = useState(false);
  const [selectedChat, setSelectedChat] = useState<{
    chatId?: string;
    discoverId: string;
    url?: string;
    title?: string;
  } | null>(null);
  const toggleExpand = (index: number) => {
    setExpandedItems(prev =>
      prev.includes(index)
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };
  const [topicLoading, setTopicLoading] = useState<Record<string, boolean>>({});

  const fetchTopicData = async (
    topic: string,
    page: number = 1,
    summarized: boolean = summarizedOnly,
    important: boolean = importantOnly
  ) => {
    setTopicLoading(prev => ({ ...prev, [topic]: true }));
    try {
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/discover?topic=${topic}&page=${page}&pageSize=10&summarized=${summarized}&important=${important}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      const data = await res.json();

      if (!res.ok) {
        throw new Error(data.message);
      }

      setDiscoverMap(prev => ({
        ...prev,
        [topic]: data,
      }));

      setCurrentPage(prev => ({
        ...prev,
        [topic]: page,
      }));

      setTimeout(() => {
        const tabPanel = document.querySelector(`[data-topic="${topic}"]`);
        if (tabPanel) {
          tabPanel.scrollTop = 0;
        }
      }, 0);
    } catch (err: any) {
      console.error('Error fetching data:', err.message);
      toast.error('获取数据失败');
    } finally {
      setTopicLoading(prev => ({ ...prev, [topic]: false }));
    }
  };

  const fetchTopics = async () => {
    try {
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/discover/topics`);
      const data = await res.json();
      if (!res.ok) {
        throw new Error(data.message);
      }
      setTopics(data.topics);
      return data.topics;
    } catch (err: any) {
      console.error('Error fetching topics:', err.message);
      toast.error('获取主题列表失败');
      return [];
    }
  };

  useEffect(() => {
    const init = async () => {
      try {
        const topicList = await fetchTopics();
        if (topicList.length > 0) {
          const savedTab = localStorage.getItem('selectedDiscoverTab');
          const savedTopicId = savedTab || topicList[0].id;
          const tabIndex = topicList.findIndex((t: { id: any }) => t.id === savedTopicId);

          setSelectedTab(tabIndex >= 0 ? tabIndex : 0);
          await fetchTopicData(savedTopicId);
        }
      } finally {
        setLoading(false);
      }
    };

    init();
  }, []);



  // 清理触摸状态的副作用
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // 页面隐藏时重置状态
        touchStateRef.current.isTracking = false;
        setPullDistance(0);
        setIsPulling(false);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  const [refreshing, setRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [isPulling, setIsPulling] = useState(false);

  // 优化的触摸状态管理
  const touchStateRef = useRef({
    startY: 0,
    startTime: 0,
    isTracking: false,
    lastY: 0,
  });

  const threshold = 80; // 增加阈值以提供更好的用户体验
  const maxPullDistance = 120; // 最大拉动距离
  const dampingFactor = 0.6; // 阻尼系数，让拉动感觉更自然

  const handlePullToRefresh = useCallback(
    async (topic: string) => {
      if (refreshing) return; // 防止重复触发

      setRefreshing(true);
      try {
        await fetchTopicData(topic, 1);
        toast.success('数据已更新', {
          duration: 1500,
        });
      } finally {
        setRefreshing(false);
      }
    },
    [refreshing] // 添加 refreshing 依赖
  );

  // 优化的触摸开始处理
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (e.touches.length !== 1 || refreshing) return;

    const touch = e.touches[0];
    touchStateRef.current = {
      startY: touch.clientY,
      startTime: Date.now(),
      isTracking: true,
      lastY: touch.clientY,
    };

    setIsPulling(false);
    setPullDistance(0);
  }, [refreshing]);

  // 优化的触摸移动处理，使用 requestAnimationFrame 提升性能
  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!touchStateRef.current.isTracking || e.touches.length !== 1 || refreshing) return;

    const panel = e.currentTarget as HTMLElement;
    const touch = e.touches[0];
    const currentY = touch.clientY;
    const deltaY = currentY - touchStateRef.current.startY;
    const isAtTop = panel.scrollTop <= 0;

    // 只在顶部且向下拉动时处理
    if (isAtTop && deltaY > 0) {
      // 使用 requestAnimationFrame 优化动画性能
      requestAnimationFrame(() => {
        // 应用阻尼效果，让拉动感觉更自然
        const dampedDistance = Math.min(deltaY * dampingFactor, maxPullDistance);

        setPullDistance(dampedDistance);
        setIsPulling(true);

        // 更新最后位置
        touchStateRef.current.lastY = currentY;
      });

      // 阻止默认滚动行为
      e.preventDefault();
    } else if (isPulling && deltaY <= 0) {
      // 如果用户向上滑动，重置状态
      requestAnimationFrame(() => {
        setPullDistance(0);
        setIsPulling(false);
      });
    }
  }, [refreshing, isPulling, dampingFactor, maxPullDistance]);

  // 优化的触摸结束处理
  const handleTouchEnd = useCallback(async (_e: React.TouchEvent, topic: string) => {
    if (!touchStateRef.current.isTracking || refreshing) return;

    const endTime = Date.now();
    const duration = endTime - touchStateRef.current.startTime;

    // 重置触摸状态
    touchStateRef.current.isTracking = false;

    // 检查是否应该触发刷新
    if (isPulling && pullDistance >= threshold && duration > 100) { // 最小持续时间检查
      await handlePullToRefresh(topic);
    }

    // 重置UI状态
    setPullDistance(0);
    setIsPulling(false);
  }, [refreshing, isPulling, pullDistance, threshold, handlePullToRefresh]);

  // 主题切换统一处理
  const handleSelectTopic = (topicId: string, idx: number) => {
    setSelectedTab(idx);
    localStorage.setItem('selectedDiscoverTab', topicId);
    if (!discoverMap[topicId]) {
      fetchTopicData(topicId);
    }
  };



  const selectedTopic = topics[selectedTab];

  return loading ? (
    <LoadingSpinner />
  ) : (
    <Layout>
      <div className="flex flex-col min-h-screen bg-light-50 dark:bg-dark-50">
        <DiscoverTopBar
          topics={topics}
          selectedTopicId={selectedTopic?.id}
          onSelectTopic={handleSelectTopic}
          summarizedOnly={summarizedOnly}
          importantOnly={importantOnly}
          onSummarizedChange={checked => {
            setSummarizedOnly(checked);
            topics.forEach(topic => {
              fetchTopicData(topic.id, 1, checked, importantOnly);
            });
          }}
          onImportantChange={checked => {
            setImportantOnly(checked);
            topics.forEach(topic => {
              if (discoverMap[topic.id]) {
                fetchTopicData(topic.id, 1, summarizedOnly, checked);
              }
            });
          }}
        />
        <main className="flex-1 flex flex-col pt-[104px] lg:pt-[72px]">
          <div className="flex flex-1 min-h-0">
            <DiscoverSidebar
              topics={topics}
              selectedTopicId={selectedTopic?.id}
              onSelectTopic={handleSelectTopic}
              className="h-full"
            />
            <div className="flex-1 lg:pl-5 pr-1 lg:pr-5">
              <div className="flex-1 min-h-0 relative">
                {topics.length > 0 && selectedTopic && (
                  <div
                    className={cn(
                      "flex-1 min-h-0 relative overflow-y-auto overscroll-none",
                      // 优化滚动性能
                      "scroll-smooth"
                    )}
                    data-topic={selectedTopic.id}
                    onTouchStart={handleTouchStart}
                    onTouchMove={handleTouchMove}
                    onTouchEnd={e => handleTouchEnd(e, selectedTopic.id)}
                    style={{
                      // 优化触摸滚动
                      WebkitOverflowScrolling: 'touch',
                      // 防止过度滚动
                      overscrollBehavior: 'contain',
                    }}
                  >
                    <div
                      className={cn(
                        "transition-transform ease-out",
                        refreshing ? "duration-300" : "duration-200"
                      )}
                      style={{
                        transform: `translateY(${pullDistance}px)`,
                        willChange: isPulling || refreshing ? 'transform' : 'auto',
                      }}
                    >
                      {/* 优化的刷新指示器 */}
                      <div
                        className={cn(
                          "absolute top-0 left-0 right-0 flex flex-col justify-center items-center h-16 -translate-y-full transition-all duration-300",
                          (isPulling || refreshing) ? "opacity-100" : "opacity-0"
                        )}
                      >
                        <div className="flex items-center space-x-2">
                          {refreshing ? (
                            <>
                              <LoadingSpinner />
                              <span className="text-sm text-black/70 dark:text-white/70">
                                正在刷新...
                              </span>
                            </>
                          ) : (
                            <>
                              <div
                                className={cn(
                                  "w-4 h-4 rounded-full border-2 transition-all duration-200",
                                  pullDistance >= threshold
                                    ? "border-green-500 bg-green-500/20 rotate-180"
                                    : "border-gray-400 dark:border-gray-500"
                                )}
                              >
                                <div className={cn(
                                  "w-full h-full rounded-full transition-all duration-200",
                                  pullDistance >= threshold ? "bg-green-500" : ""
                                )} />
                              </div>
                              <span className="text-sm text-black/70 dark:text-white/70">
                                {pullDistance >= threshold ? '松开刷新' : '下拉刷新'}
                              </span>
                            </>
                          )}
                        </div>
                        {/* 进度指示器 */}
                        {!refreshing && (
                          <div className="w-16 h-1 bg-gray-200 dark:bg-gray-700 rounded-full mt-2 overflow-hidden">
                            <div
                              className="h-full bg-blue-500 transition-all duration-100 ease-out"
                              style={{
                                width: `${Math.min((pullDistance / threshold) * 100, 100)}%`
                              }}
                            />
                          </div>
                        )}
                      </div>
                      <div className="flex flex-col space-y-1.5 p-1 pb-5">
                        {discoverMap[selectedTopic.id]?.blogs?.map((item, i) => (
                          <ArticleItem
                            key={item.id}
                            item={item}
                            index={i}
                            expandedItems={expandedItems}
                            toggleExpand={toggleExpand}
                            onSelect={item => {
                              setSelectedChat({
                                chatId: item.chatId,
                                discoverId: item.id,
                                url: !item.chatId ? item.url : undefined,
                                title: `追踪： ${item.titleZh || item.title}`,
                              });
                              setIsDrawerOpen(true);
                            }}
                          />
                        ))}
                      </div>
                      {/* 分页控制 */}
                      {discoverMap[selectedTopic.id]?.pagination && (
                        <div className="sticky bottom-0 z-10 flex justify-center items-center space-x-2 py-2 bg-light-50/80 dark:bg-dark-50/80 backdrop-blur-sm border-t border-black/5 dark:border-white/5">
                          <button
                            onClick={() =>
                              fetchTopicData(selectedTopic.id, currentPage[selectedTopic.id] - 1)
                            }
                            disabled={currentPage[selectedTopic.id] <= 1}
                            className="px-3 py-1.5 rounded text-sm disabled:opacity-50 hover:bg-light-100 dark:hover:bg-dark-100 transition-colors"
                          >
                            上一页
                          </button>
                          <span className="text-sm text-black/70 dark:text-white/70">
                            {currentPage[selectedTopic.id]} /{' '}
                            {discoverMap[selectedTopic.id].pagination.totalPages}
                          </span>
                          <button
                            onClick={() =>
                              fetchTopicData(selectedTopic.id, currentPage[selectedTopic.id] + 1)
                            }
                            disabled={
                              currentPage[selectedTopic.id] >=
                              discoverMap[selectedTopic.id].pagination.totalPages
                            }
                            className="px-3 py-1.5 rounded text-sm disabled:opacity-50 hover:bg-light-100 dark:hover:bg-dark-100 transition-colors"
                          >
                            下一页
                          </button>
                        </div>
                      )}
                    </div>
                    {topicLoading[selectedTopic.id] && (
                      <div className="absolute inset-0 bg-black/5 dark:bg-white/5 flex items-center justify-center z-10 backdrop-blur-[1px]">
                        <LoadingSpinner />
                      </div>
                    )}
                  </div>
                )}
              </div>
              {/* 右侧抽屉 */}
              {isDrawerOpen && (
                <ChatDrawer
                  isOpen={isDrawerOpen}
                  onClose={() => {
                    setIsDrawerOpen(false);
                    setSelectedChat(null);
                  }}
                  selectedChat={selectedChat}
                  onChatIdCreated={newChatId => {
                    setSelectedChat(prev =>
                      prev
                        ? {
                            ...prev,
                            chatId: newChatId,
                          }
                        : null
                    );
  
                    if (selectedChat?.discoverId) {
                      setDiscoverMap(prev => {
                        const newMap = { ...prev };
                        Object.keys(newMap).forEach(topic => {
                          if (newMap[topic]?.blogs) {
                            newMap[topic] = {
                              ...newMap[topic],
                              blogs: newMap[topic].blogs.map(item =>
                                item.id === selectedChat.discoverId
                                  ? { ...item, chatId: newChatId }
                                  : item
                              ),
                            };
                          }
                        });
                        return newMap;
                      });
                    }
                  }}
                />
              )}
            </div>
          </div>
        </main>
      </div>
    </Layout>
  );
};

export default DiscoverPageContent;
