import React from 'react';
import { TimerIcon, HelpCircleIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Topic } from '@/types/discover';
import { useRouter } from 'next/navigation';

// 响应式判断 Hook
function useIsDesktop() {
  const [isDesktop, setIsDesktop] = React.useState(
    typeof window === 'undefined' ? false : window.innerWidth >= 1024
  );
  React.useEffect(() => {
    function handleResize() {
      setIsDesktop(window.innerWidth >= 1024);
    }
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  return isDesktop;
}

interface DiscoverTopBarProps {
  topics: Topic[];
  selectedTopicId: string;
  onSelectTopic: (topicId: string, index: number) => void;
  summarizedOnly: boolean;
  importantOnly: boolean;
  onSummarizedChange: (checked: boolean) => void;
  onImportantChange: (checked: boolean) => void;
  className?: string;
}

const DiscoverTopBar: React.FC<DiscoverTopBarProps> = ({
  topics,
  selectedTopicId,
  onSelectTopic,
  summarizedOnly,
  importantOnly,
  onSummarizedChange,
  onImportantChange,
  className = '',
}) => {
  const router = useRouter();
  const selectedIndex = React.useMemo(
    () => topics.findIndex(t => t.id === selectedTopicId),
    [topics, selectedTopicId]
  );

  // 头部筛选栏
  const Header = (
    <div
      className={cn(
        'flex flex-row items-center gap-2 px-4 py-3 bg-gradient-to-r from-blue-50/80 via-white/90 to-blue-100/80 dark:from-blue-900/30 dark:via-[#181c20]/90 dark:to-blue-900/30 shadow-md border-b border-light-200 dark:border-dark-200 sm:rounded-t-2xl',
        'backdrop-blur-md',
        // PC端(lg及以上)增强分隔线与间距
        'lg:border-b lg:border-gray-200 lg:mb-2',
        'lg:rounded-t-2xl',
        className
      )}
      style={{ minHeight: 56, position: 'relative' }}
    >
      {/* 左侧：图标+标题 */}
      <div className="flex items-center gap-2 flex-shrink-0">
        <TimerIcon className="w-5 h-5 sm:w-6 sm:h-6 flex-shrink-0 text-blue-500 dark:text-blue-400 drop-shadow" />
        <h1 className="text-lg sm:text-xl md:text-2xl font-extrabold tracking-tight flex-1 min-w-0 text-blue-700 dark:text-blue-300 flex items-center gap-2 overflow-hidden whitespace-nowrap text-ellipsis">
          新闻追踪
          <span className="relative flex h-3 w-3 flex-shrink-0">
            <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
            <span className="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
          </span>
        </h1>
      </div>
      {/* 中间占位（如需扩展可用） */}
      <div className="flex-1 min-w-0"></div>
      {/* 右侧：筛选项按钮组 */}
      <div className="flex gap-1 sm:gap-2 flex-shrink-0 flex-nowrap max-w-full overflow-x-auto">
        <button
          type="button"
          className={cn(
            'flex items-center gap-1 px-2 py-1 rounded-full text-[11px] font-semibold transition-all duration-200 border shadow-sm sm:px-4 sm:py-1.5 sm:text-xs',
            summarizedOnly
              ? 'bg-blue-500 text-white border-blue-500 shadow-md'
              : 'bg-gray-100 dark:bg-dark-100 text-gray-500 dark:text-gray-300 border-gray-200 dark:border-dark-200 hover:bg-blue-100 dark:hover:bg-blue-900/20',
          )}
          style={{ minHeight: 32 }}
          onClick={() => onSummarizedChange(!summarizedOnly)}
        >
          <svg className="w-3 h-3 mr-1 sm:w-3.5 sm:h-3.5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 16 16"><path d="M3 8h10M8 3v10" /></svg>
          <span className="sm:hidden">已总结</span>
          <span className="hidden sm:inline">仅显示已总结</span>
        </button>
        <button
          type="button"
          className={cn(
            'flex items-center gap-1 px-2 py-1 rounded-full text-[11px] font-semibold transition-all duration-200 border shadow-sm sm:px-4 sm:py-1.5 sm:text-xs',
            importantOnly
              ? 'bg-pink-500 text-white border-pink-500 shadow-md'
              : 'bg-gray-100 dark:bg-dark-100 text-gray-500 dark:text-gray-300 border-gray-200 dark:border-dark-200 hover:bg-pink-100 dark:hover:bg-pink-900/20',
          )}
          style={{ minHeight: 32 }}
          onClick={() => onImportantChange(!importantOnly)}
        >
          <svg className="w-3 h-3 mr-1 sm:w-3.5 sm:h-3.5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 16 16"><circle cx="8" cy="8" r="6" /></svg>
          <span className="sm:hidden">重点</span>
          <span className="hidden sm:inline">仅显示重点</span>
        </button>
        <button
          type="button"
          className={cn(
            'flex items-center gap-1 px-2 py-1 rounded-full text-[11px] font-semibold transition-all duration-200 border shadow-sm sm:px-4 sm:py-1.5 sm:text-xs',
            'bg-gray-100 dark:bg-dark-100 text-blue-500 dark:text-blue-300 border-blue-200 dark:border-blue-800 hover:bg-blue-100 dark:hover:bg-blue-900/20'
          )}
          style={{ minHeight: 32 }}
          onClick={() => router.push('/ask')}
        >
          <HelpCircleIcon className="w-3 h-3 mr-1 sm:w-3.5 sm:h-3.5" />
          <span className="sm:hidden">提问</span>
          <span className="hidden sm:inline">提问</span>
        </button>
      </div>
      {/* subtle 阴影/分割线 */}
      <div className="absolute left-0 bottom-0 w-full h-[1.5px] bg-gradient-to-r from-transparent via-gray-200 dark:via-dark-200 to-transparent shadow-sm pointer-events-none" />
    </div>
  );

  // 移动端 Tabs
  const Tabs = (
    <nav
      className={cn(
        'flex lg:hidden w-full overflow-x-auto bg-gradient-to-r from-white/95 via-blue-50/80 to-white/95 dark:from-[#181c20]/95 dark:via-blue-900/20 dark:to-[#181c20]/95 px-2 shadow-sm',
      )}
    >
      <ul className="flex flex-row space-x-2 min-w-fit py-2">
        {topics.map((topic, idx) => (
          <li key={topic.id}>
            <button
              className={cn(
                'px-4 py-1.5 rounded-full text-sm font-medium outline-none whitespace-nowrap transition-all duration-200 shadow-sm',
                selectedIndex === idx
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'bg-gray-100 dark:bg-dark-100 text-gray-500 dark:text-gray-300 hover:bg-blue-100 dark:hover:bg-blue-900/30',
              )}
              onClick={() => onSelectTopic(topic.id, idx)}
            >
              {topic.name}
            </button>
          </li>
        ))}
      </ul>
    </nav>
  );


  return (
    <div className="fixed top-0 left-0 right-0 z-50 w-full bg-transparent backdrop-blur-md">
      {Header}
      <div className="w-full">{Tabs}</div>
    </div>
  );
};

export default DiscoverTopBar;
